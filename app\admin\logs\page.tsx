"use client";

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertTriangle, Shield, Info, AlertCircle, Trash2, RefreshCw } from 'lucide-react';

interface LogEntry {
  timestamp: string;
  level: string;
  category: string;
  message: string;
  ip?: string;
  userAgent?: string;
  userId?: string;
  details?: Record<string, unknown>;
}

interface LogStats {
  total: number;
  byCategory: Record<string, number>;
  byHour: Record<string, number>;
}

interface LogResponse {
  logs: LogEntry[];
  statistics: LogStats | null;
  level: string;
  total: number;
}

export default function LogsPage() {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [statistics, setStatistics] = useState<LogStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [level, setLevel] = useState('SECURITY');
  const [limit, setLimit] = useState('50');
  const [error, setError] = useState<string | null>(null);

  const fetchLogs = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/admin/logs?level=${level}&limit=${limit}&stats=true`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: LogResponse = await response.json();
      setLogs(data.logs);
      setStatistics(data.statistics);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch logs');
      console.error('Error fetching logs:', err);
    } finally {
      setLoading(false);
    }
  }, [level, limit]);

  const clearOldLogs = async () => {
    if (!confirm('Are you sure you want to clear logs older than 30 days?')) {
      return;
    }

    try {
      const response = await fetch('/api/admin/logs?days=30', {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error('Failed to clear logs');
      }
      
      alert('Old logs cleared successfully');
      fetchLogs(); // Refresh the logs
    } catch (err) {
      alert('Failed to clear logs: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  const getLevelIcon = (logLevel: string) => {
    switch (logLevel) {
      case 'SECURITY':
        return <Shield className="h-4 w-4" />;
      case 'ERROR':
        return <AlertCircle className="h-4 w-4" />;
      case 'WARN':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getLevelColor = (logLevel: string) => {
    switch (logLevel) {
      case 'SECURITY':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'ERROR':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'WARN':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('it-IT');
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">Caricamento log...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Log di Sicurezza</h1>
          <p className="text-muted-foreground">
            Monitora gli eventi di sicurezza e le attività del sistema
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button onClick={fetchLogs} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Aggiorna
          </Button>
          <Button onClick={clearOldLogs} variant="destructive">
            <Trash2 className="h-4 w-4 mr-2" />
            Pulisci Log Vecchi
          </Button>
        </div>
      </div>

      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Filtri</CardTitle>
        </CardHeader>
        <CardContent className="flex gap-4">
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Livello Log</label>
            <Select value={level} onValueChange={setLevel}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="SECURITY">Sicurezza</SelectItem>
                <SelectItem value="ERROR">Errori</SelectItem>
                <SelectItem value="WARN">Avvisi</SelectItem>
                <SelectItem value="INFO">Info</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Numero Log</label>
            <Select value={limit} onValueChange={setLimit}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value="200">200</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Totale Eventi (24h)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.total}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Categorie Principali</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                {Object.entries(statistics.byCategory)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 3)
                  .map(([category, count]) => (
                    <div key={category} className="flex justify-between text-sm">
                      <span>{category}</span>
                      <span className="font-medium">{count}</span>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Log Visualizzati</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{logs.length}</div>
              <div className="text-sm text-muted-foreground">su {limit} richiesti</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-4 w-4" />
              <span>Errore: {error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Logs */}
      <Card>
        <CardHeader>
          <CardTitle>Log Eventi</CardTitle>
          <CardDescription>
            Eventi recenti di livello {level.toLowerCase()}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {logs.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Nessun log trovato per i criteri selezionati
            </div>
          ) : (
            <div className="space-y-3">
              {logs.map((log, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge className={getLevelColor(log.level)}>
                        {getLevelIcon(log.level)}
                        <span className="ml-1">{log.level}</span>
                      </Badge>
                      <Badge variant="outline">{log.category}</Badge>
                      <span className="text-sm text-muted-foreground">
                        {formatTimestamp(log.timestamp)}
                      </span>
                    </div>
                  </div>
                  
                  <div className="text-sm">
                    <strong>Messaggio:</strong> {log.message}
                  </div>
                  
                  {(log.ip || log.userAgent || log.userId) && (
                    <div className="text-xs text-muted-foreground space-y-1">
                      {log.ip && <div><strong>IP:</strong> {log.ip}</div>}
                      {log.userId && <div><strong>User ID:</strong> {log.userId}</div>}
                      {log.userAgent && <div><strong>User Agent:</strong> {log.userAgent}</div>}
                    </div>
                  )}
                  
                  {log.details && (
                    <details className="text-xs">
                      <summary className="cursor-pointer text-muted-foreground">
                        Dettagli tecnici
                      </summary>
                      <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                        {JSON.stringify(log.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
